package com.example.todo_backend.services;

import com.example.todo_backend.models.Todo;
import com.example.todo_backend.repository.TodoRepository;
import org.springframework.stereotype.Service;


import java.util.List;


@Service
public class TodoService {
    private final TodoRepository todoRepository;

    public TodoService(TodoRepository todoRepository) {

        this.todoRepository = todoRepository;
    }

    //get all todos
    public List<Todo> getAllTodos() {

        return todoRepository.findAll();
    }

    // get todo by id
    public Todo getTodoById(Long id){
        return todoRepository.findById(id).orElseThrow(()->new RuntimeException("Todo not found with id " + id));
    }

    // create a new todo
    public Todo createTodo(Todo todo){

        return todoRepository.save(todo);
    }

    //update todo

    public Todo updateTodo(Long id, Todo todoDetails){
        Todo todo = getTodoById(id);
        todo.setTitle(todoDetails.getTitle());
        todo.setDescription(todoDetails.getDescription());
        todo.setCompleted(todoDetails.isCompleted());
        return todoRepository.save(todo);
//        return todo;
    }
    //delete todo
    public void deleteTodo(Long id){
        Todo todo = getTodoById(id);
        todoRepository.delete(todo);
    }
    //mark todo as complete
    public Todo markAsComplete(Long id){
        Todo todo = getTodoById(id);
        todo.setCompleted(true);
        return todoRepository.save(todo);
//        return todo;
    }

}
